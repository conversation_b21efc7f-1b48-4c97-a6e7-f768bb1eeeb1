@{
  PekHtml.AppendCssFileParts("~/public/css/pageCss/finance.css");
}
@* 财务资质信息 *@
<div class="main">
  <!-- 面包屑 -->
  <div class="breadBox">
    <a href="/">首页</a>
    <div>></div>
    <a class="textSelect" href="#">
      供应商合作
    </a>
    <div>></div>
    <a class="textSelect" href="#">
      商家入驻申请
    </a>
  </div>
  <div class="layui-container">
    <!-- 步骤条 -->
    <div class="steps-bar">
      <div class="step-item active">
        <div class="step-circle">
          <i class="layui-icon layui-icon-ok"></i>
        </div>
        <p>签订入驻协议</p>
      </div>
      <div class="step-item active">
        <div class="step-circle">
          <i class="layui-icon layui-icon-ok"></i>
        </div>
        <p>公司资质信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">3</div>
        <p>财务资质信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">4</div>
        <p>店铺经营信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">5</div>
        <p>合同签订</p>
      </div>
      <div class="step-item">
        <div class="step-circle">6</div>
        <p>店铺开通</p>
      </div>
    </div>

    <!-- 协议内容卡片 -->
    <div class="center-card layui-card">

      <div class="layui-card tip">
        <div class="layui-card-header">注意事项:</div>
        <div class="layui-card-body">
          以下所需要上传的电子版资质文件仅支持JPG\GIF\PNG格式图片，大小请控制在1M之内。
        </div>
      </div>

      <form class="layui-form">
        <h3 class="big-title">开户银行信息</h3>
        <div class="layui-col-md6 layui-col-xs6">
          <div class="layui-form-item">
            <label class="layui-form-label">银行开户名</label>
            <div class="layui-input-inline">
              <input type="text" name="accountName" placeholder="请输入银行开户名" autocomplete="off" class="layui-input"
                value="深圳某某科技有限公司" lay-verify="required">
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">公司银行账户</label>
              <div class="layui-input-inline">
                <input type="number" name="bankAccount" autocomplete="off" lay-affix="clear" placeholder="请输入公司银行账户"
                  class="layui-input" lay-verify="required" value="************">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">开户银行支行名称</label>
              <div class="layui-input-inline">
                <input type="text" name="address" placeholder="请输入开户银行支行名称" autocomplete="off" class="layui-input"
                  value="中国银行上深圳市浦东分行" lay-verify="required">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">@T("开户银行所在地")</label>
              <div class="layui-input-inline">
                <select name="quiz1" lay-search lay-verify="required" lay-filter="province">
                  <option value="">请选择省</option>
                  <option value="浙江">浙江省</option>
                  <option value="江西">江西省</option>
                  <option value="福建">福建省</option>
                  <option value="天津">天津市</option>
                  <option value="河北">河北省</option>
                  <option value="山西">山西省</option>

                </select>
              </div>
              <label class="layui-form-label hide">@T("开户银行所在地")</label>
              <div class="layui-input-inline">
                <input type="checkbox" id="agreeCheck" name="agree" lay-skin="primary" title="此账号为结算账号"
                  lay-filter="agree">
              </div>

            </div>
          </div>
        </div>
        <h3 class="big-title">结算账号信息</h3>
        <div class="layui-col-md6 layui-col-xs6">
          <div class="layui-form-item">
            <label class="layui-form-label">银行开户名</label>
            <div class="layui-input-inline">
              <input type="text" name="accountName2" placeholder="请输入银行开户名" autocomplete="off" class="layui-input"
                value="深圳某某科技有限公司2" lay-verify="required">
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">公司银行账户</label>
              <div class="layui-input-inline">
                <input type="number" name="bankAccount2" autocomplete="off" lay-affix="clear" placeholder="请输入公司银行账户"
                  class="layui-input" lay-verify="required" value="************">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">开户银行支行名称</label>
              <div class="layui-input-inline">
                <input type="text" name="address2" placeholder="请输入开户银行支行名称" autocomplete="off" class="layui-input"
                  value="中国银行上深圳市浦东分行2" lay-verify="required">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-col-md6 layui-col-xs6">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("开户银行所在地")</label>
            <div class="layui-input-inline">
              <select name="quiz2" lay-search lay-verify="required">
                <option value="">请选择省</option>
                <option value="浙江">浙江省</option>
                <option value="江西">江西省</option>
                <option value="福建">福建省</option>
                <option value="天津">天津市</option>
                <option value="河北">河北省</option>
                <option value="山西">山西省</option>
              </select>
            </div>
          </div>
        </div>


        <div class="layui-row">
          <div class="layui-form-item btnBox">
            <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit lay-filter="demo1">下一步</button>
          </div>
        </div>
      </form>
    </div>



  </div>
</div>
<div class="bug"></div>
<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=4466b7f03b3ccc26a491b9f08d5ee6ef">
</script>


<script>
  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var util = layui.util;

      // 渲染表单
      form.render();
      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值
        console.log("field", field);



        return false; // 阻止默认 form 跳转
      });

      // 勾选“此账号为结算账号”时，开户银行信息 = 结算账号信息，取消时还原
      var settleAccountCache = {};

      // 同步开户银行信息到结算账号信息的函数
      function syncBankInfoToSettle() {
        if ($('#agreeCheck').prop('checked')) {
          $("input[name='accountName2']").val($("input[name='accountName']").val());
          $("input[name='bankAccount2']").val($("input[name='bankAccount']").val());
          $("input[name='address2']").val($("input[name='address']").val());
          $("select[name='quiz2']").val($("select[name='quiz1']").val());
          form.render('select');
        }
      }

      // 监听开户银行信息变化，实时同步到结算账号信息
      $("input[name='accountName'], input[name='bankAccount'], input[name='address']").on('input', function () {
        syncBankInfoToSettle();
      });

      // 监听开户银行所在地下拉框变化
      form.on('select()', function (data) {
        if (data.elem.name === 'quiz1') {
          syncBankInfoToSettle();
        }
      });

      form.on('checkbox(agree)', function (data) {
        if (data.elem.checked) {
          // 先缓存结算账号信息
          settleAccountCache = {
            accountName2: $("input[name='accountName2']").val(),
            bankAccount2: $("input[name='bankAccount2']").val(),
            address2: $("input[name='address2']").val(),
            quiz2: $("select[name='quiz2']").val()
          };
          // 复制开户银行信息到结算账号信息并禁用编辑
          syncBankInfoToSettle();
          $("input[name='accountName2'], input[name='bankAccount2'], input[name='address2']").prop('disabled', true);
          $("select[name='quiz2']").prop('disabled', true);
          form.render('select');
        } else {
          // 还原结算账号信息并启用编辑
          $("input[name='accountName2']").val(settleAccountCache.accountName2).prop('disabled', false);
          $("input[name='bankAccount2']").val(settleAccountCache.bankAccount2).prop('disabled', false);
          $("input[name='address2']").val(settleAccountCache.address2).prop('disabled', false);
          $("select[name='quiz2']").val(settleAccountCache.quiz2).prop('disabled', false);
          form.render('select');
        }
      });

       @* 判断所有必填项是否有值  *@
    });



  });


</script>