﻿@using B2B2CShop.Dto
@using B2B2CShop.Entity
@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
    Layout = "_RootNoHeader";
    int id = ViewBag.Id;
    int addressType = ViewBag.AddressType;
}

<style>
    .layui-layer-title {
        font-size: 1vw;
        letter-spacing: 1px;
    }

    .editAddrBox {
        width: 30vw;
        max-width: 500px;
        min-width: 400px;
        max-height: 80vh;
        overflow: auto;
        padding: 1vw 1vw .2vw 1vw;
    }

    .distributionBox {
        width: 100%;
        padding: 7px;
        justify-content: left;
        background-color: #F2F2F2;
    }

    .layui-form-label {
        color: var(--text-color2);
    }

    .layui-form-item {
        width: 100%;
        display: flex;
        position: relative;
        /* border: 1px solid var(--line); */
    }

    .layui-inline {
        width: 100%;
        margin-right: 0px !important;
        display: flex;
    }

        .layui-inline > div:last-child {
            margin-right: 0px !important;
            padding-right: 0px !important;
        }

    .layui-input-inline {
        flex: 1;
        /* background-color: red; */
    }

    .failText {
        position: absolute;
        left: 120px;
        top: 92%;
        font-size: 12px;
        color: var(--red);
        display: none;
    }

    .dialog_editAddr > .layui-layer-btn {
        padding: 0px 0px;
        margin: 0px;
        margin-bottom: 20px;
        width: 100%;
        display: flex;
        /* border: 1px solid ; */
        font-size: 17px;
        height: 50px;
    }
        /* 取消按钮 */
        .dialog_editAddr > .layui-layer-btn > a:nth-child(1) {
            margin-left: 130px;
            width: 20%;
            min-width: 120px;
            background-color: white;
            color: var(--text-color);
            border: 1px solid #C4C6CF;
            text-align: center;
            height: 40px;
            line-height: 40px;
            border-radius: 7px;
        }
        /* 取消按钮 */
        .dialog_editAddr > .layui-layer-btn > a:nth-child(2) {
            margin-left: 40px;
            width: 20%;
            min-width: 120px;
            background-color: var(--blue-deep);
            color: white;
            text-align: center;
            height: 40px;
            line-height: 40px;
            border-radius: 7px;
            margin-right: auto;
        }

    .layui-anim-upbit {
        z-index: 9999;
    }

    .cancelBtn {
        margin-left: 110px;
        width: 25%;
        min-width: 104px;
        background-color: white;
        color: var(--text-color);
        border: 1px solid #C4C6CF;
        text-align: center;
        height: 40px;
        line-height: 40px;
        border-radius: 7px;
    }

    .saveBtn {
        margin-left: auto !important;
        margin-right: auto;
        width: 25%;
        min-width: 104px;
        background-color: var(--blue-deep);
        color: white;
        text-align: center;
        height: 40px;
        line-height: 40px;
        border-radius: 7px;
    }
</style>
<script asp-location="Head">
    var NoResultsFound = '@T("No results found")';
</script>
<div style="margin:20px">
    <form class="layui-form" lay-filter="form">
        <div class="layui-form-item">
            <div class="distributionBox flex">
                <div class="layui-form-label" style="padding-top: 0;padding-bottom: 0;">@T("当前配送至")</div>
                <div style="margin-left: 10px;">
                    <select name="TwoLetterIsoCode" lay-filter="Country"  lay-verify="required" lay-search lay-vertype="tips" id="Country">
                        <option value="">@T("请选择你的国家")</option>
                        @foreach (CountryDto item in ViewBag.CountryList)
                        {
                            <option value="@item.TwoLetterIsoCode">@item.Name</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label required">@T("收货人姓名")</label>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="tel" name="Name2" lay-verify="name2" autocomplete="off" id="name2"
                           lay-affix="clear" class="layui-input" placeholder="@T("名字")">
                </div>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="tel" name="Name1" lay-verify="name1" lay-verType="tips" autocomplete="off" id="name1"
                           lay-affix="clear" class="layui-input" placeholder="@T("姓氏")">
                </div>

            </div>
            <div class="failText name1">@T("请填写姓氏")</div>
            <div class="failText name2" style="left: 65%;">@T("请填写名字")</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label required">@T("手机号码")</label>
@*                 <div class="layui-input-inline layui-input-wrap">
                    <select name="modules" name="modules" lay-verify="modules" id="modules" lay-filter="modules" lay-search>
                        <option value="">@T("请选择")</option>
                        @foreach (TelAreaCode areacode in ViewBag.AreaCodes)
                        {
                            <option value="@areacode.Id">@(areacode.CountryId + " " + areacode.AreaCode)</option>
                        }
                    </select>
                    <div class="failText modules" style="left:10px;top:70%;">@T("请选择手机区号")</div>
                </div> *@
                <div class="layui-input-inline layui-input-wrap">
                    <input type="text" name="Phone" lay-verify="phone" autocomplete="off" lay-affix="clear" id="phone"
                           class="layui-input" placeholder="@T("手机号")">
                    <div class="failText phone" style="left:10px;top:70%;">@T("请输入正确的手机号")</div>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="dizhi">
            <div class="layui-inline">
                <label class="layui-form-label required">@T("地址信息")</label>
                <div class="layui-input-inline layui-input-wrap" id="sheng">
                    <select lay-verify="RegionId" name="RegionId" id="Region" lay-filter="Region">
                        <option value="">@T("请选择")</option>
                    </select>
                </div>
                <div class="layui-input-inline layui-input-wrap" id="shi">
                    <select lay-verify="addr1" name="CityId" id="City" lay-filter="City">
                        <option value="">@T("请选择")</option>
                    </select>
                </div>
                <div class="layui-input-inline layui-input-wrap" id="xian">
                    <select lay-verify="" name="AreaId" id="Area" lay-filter="Area">
                        <option value="">@T("请选择")</option>
                    </select>
                </div>
            </div>
            <div class="failText addr1">@T("请选择完整的地址信息")</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label required">@T("详细地址")</label>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="text" name="AddressDetail" lay-verify="addr2" autocomplete="off" lay-affix="clear" id="addressDetail"
                           class="layui-input" placeholder="@T("请输入详细地址信息")">
                </div>
            </div>
            <div class="failText addr2">@T("例如1栋1单元101室")</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("邮政编码")</label>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="text" name="ZipCode" autocomplete="off" lay-affix="clear" id="zipCode" lay-verify="zipcode"
                           class="layui-input" placeholder="@T("请输入")">
                </div>
            </div>
            <div class="failText zipcode">@T("请输入邮政编码")</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("公司名称")</label>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="text" name="CompanyName" autocomplete="off" lay-affix="clear" id="CompanyName"
                           class="layui-input" placeholder="@T("请输入")">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">@T("税号")</label>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="tel" name="TaxIdType" autocomplete="off" lay-reqtext="@T("请填写手机号")" id="TaxIdType"
                           lay-affix="clear" class="layui-input" placeholder="@T("税号类型")">
                </div>
                <div class="layui-input-inline layui-input-wrap">
                    <input type="tel" name="TaxId" autocomplete="off" lay-reqtext="@T("请填写手机号")" id="TaxId"
                           lay-affix="clear" class="layui-input" placeholder="@T("请输入")">
                </div>
            </div>
            <div class="failText" style="display: block;">@T("例如VAT 12345678")</div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <input type="checkbox" name="IsDefaultDelivery" title="@T("设置为默认地址")" id="IsDefaultDelivery"><br>
                <input type="checkbox" name="IsDefaultDelivery2" title="@T("发货地址与发票地址一致")" id="IsDefaultDelivery2">
            </div>
        </div>
        <div class="layui-form-item flex" style="margin-top: 40px;">
            <button class="layui-btn layui-btn-primary cancelBtn" onclick="closeDialog()">@T("取消")</button>
            <button type="submit" class="layui-btn saveBtn" lay-submit lay-filter="demo1">@T("保存")</button>
        </div>
    </form>
</div>
<script>
     function closeDialog() {
        var index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
    }
    layui.use(['form'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var verifyList = []
      var timer = 0;
        form.on("select(Country)",function (res)
        {
            // var selectedCountry = res.value;
            // var areaCodes = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.AreaCodes));
            // // 根据国家代码匹配电话区号
            // var matchedAreaCode = areaCodes.find(function (code) {
            //     return code.CountryId === selectedCountry;
            // });

            // if (matchedAreaCode) {
            //     // 设置电话区号下拉框的值
            //     $("#modules").val(matchedAreaCode.Id);
            //     form.render("select"); // 重新渲染 select
            // }

              $("#Region").empty();
              $("#City").empty();
              $('#City').append('<option value="">@T("请选择")</option>');
              $("#Area").empty();
               $('#Area').append('<option value="">@T("请选择")</option>');
              $.getJSON('@Url.Action("GetRegionsByCIdList")',{CId : res.value},function(res)
              {
                  if(res.data.length == 0)
                  {
                      $('#dizhi').hide();
                  }
                  else
                  {
                      $('#dizhi').show();
                      $('#sheng').show();
                      $('#shi').show();
                      $('#xian').show();
                      $('#Region').append('<option value="">@T("请选择")</option>');
                      for(var i = 0;i<res.data.length;i++)
                      {
                           $('#Region').append('<option value="'+res.data[i].Id+'">'+res.data[i].Name+'</option>');
                      }
                      layui.form.render("select","form")
                   }
               })
        })            
        form.on("select(Region)",function (res)
        {
            $("#City").empty();
            $("#Area").empty();
            $('#Area').append('<option value="">@T("请选择")</option>');
            $.getJSON('@Url.Action("GetAddressIdList")',{Id : res.value},function(res)
             {
                 if(res.data.length == 0){
                     $('#shi').hide();
                     $('#xian').hide();
                 }else{
                        $('#shi').show();
                        $('#xian').show();
                          $('#City').append('<option value="">@T("请选择")</option>');
                for(var i = 0;i<res.data.length;i++)
                {
                    $('#City').append('<option value="'+res.data[i].Id+'">'+res.data[i].Name+'</option>');
                }
                layui.form.render("select","form")
                 }
              
            });
         })
         form.on("select(City)",function (res)
         {
              $("#Area").empty();
              $.getJSON('@Url.Action("GetAddressIdList")',{Id : res.value},function(res)
               {
                   if(res.data.length == 0){
                       $('#xian').hide();
                   }else{
                       $('#xian').show();
                         $('#Area').append('<option value="">@T("请选择")</option>');
                        for(var i = 0;i<res.data.length;i++)
                        {
                                $('#Area').append('<option value="'+res.data[i].Id+'">'+res.data[i].Name+'</option>');
                        }
                        layui.form.render("select","form")
                   }
              });
          })
      form.render();
      // 自定义验证规则
      form.verify({
        name1:(e) => { verifyList[0] = {failIndex:0,val:e,fn:hasValue}
          return false},
        name2:(e) => { verifyList[1] = {failIndex:1,val:e,fn:hasValue}
          return false},
        modules:(e) => {verifyList[2] = {failIndex:2,val:e,fn:hasValue}
          return false},
        phone:(e) => {verifyList[3] = {failIndex:3,val:e,fn:verifyPhone}
          return false},
        // addr1:(e) => {verifyList[4] = {failIndex:4,val:e,fn:hasValue}
        //   return false},
        // addr2:(e) => {verifyList[5] = {failIndex:5,val:e,fn:hasValue}
        //   return false},
        zipcode:(e) => {verifyList[6] = {failIndex:6,val:e,fn:hasValue}
          return false}
        // name1:(e) => verifyList.push(e),
        // name2:(e) => verifyList.push(e),
        // phone:(e) => verifyList.push(e),
        // addr1:(e) => verifyList.push(e),
        // addr2:(e) => verifyList.push(e)
      });
      /** 校验是否通过 */
      function verifyFn() {
        const textList = ['@T("姓")','@T("名")','@T("手机号")','@T("地址")','@T("详细地址")','@T("邮政编码")']
        const failText = layui.$('.failText');
        let isPass = true;
        verifyList.forEach((item,index) => {
          // if (item.val && item.fn(item.val)) {
          //   console.log('验证通过',item);
          //   $(failText[item.failIndex]).hide(300)
          // }else{
          //   $(failText[item.failIndex]).slideDown(300)
          //   console.log(textList[index] ,'-->',item);
          //   console.log('item.fn(item.val)' ,'-->',item.fn(item.val));
          //   isPass = false
          // }
        })
        closeFailText(5000)
        return isPass
      }
      /** 关闭错误提示 -防抖*/
      function closeFailText(time) {
        clearTimeout(timer)
        const failText = layui.$('.failText');
        timer = setTimeout(() => {
            verifyList.forEach((item) => {
              $(failText[item.failIndex]).hide(300)
            })
          }, time);
      }
      // 监听表单元素 是否获取到焦点
      $('form').on('focus', 'input, select, textarea', function() {
        const failTextClass = this.getAttribute('name');
        // 在这里执行你的逻辑
        if (!failTextClass && $('.'+failTextClass).length >0) return;
        const failTextDom =  $('.'+failTextClass)
        failTextDom.hide(300)
        // console.log('触发',this,failTextDom.length);
      });
      // 提交事件
        form.on('submit(demo1)', function (data) {
             if (!verifyFn()) {
                return false;
            }

            var field = data.field; // 获取表单字段值

            var formData =
            {
                Id : @id,
                AddressType : @addressType,
                TwoLetterIsoCode : field.TwoLetterIsoCode,
                Name1 : field.Name1,
                Name2 : field.Name2,
                modules : field.modules,
                Phone : field.Phone,
                RegionId : field.RegionId,
                CityId : field.CityId,
                AreaId : field.AreaId,
                AddressDetail : field.AddressDetail,
                ZipCode : field.ZipCode,
                TaxIdType : field.TaxIdType,
                TaxId : field.TaxId,
                IsDefaultDelivery : field.IsDefaultDelivery,
                IsDefaultDelivery2 : field.IsDefaultDelivery2,
                CompanyName : field.CompanyName,
            };
            // 此处可执行 Ajax 等操作
            // …
            //console.log(1111,formData)
            $.post('@Url.Action("SaveAddress")',formData,function(res)
            {
                if(res.success){
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }else{
                        layui.layer.msg(res.msg);
                }
            })
            return false;
        });
    });
</script>