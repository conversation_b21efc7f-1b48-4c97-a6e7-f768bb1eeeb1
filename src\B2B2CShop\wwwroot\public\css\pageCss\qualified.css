.main {
  background-color: var(--white2);
  ;
  padding: 0 10vw;
  box-sizing: border-box;
  width: 100%;
  margin-left: 0;

}

.header2,
.headerItem {
  display: none;

}

.header {
  padding: 1vw 7vw;
}

.breadBox {
  border-bottom: none;
}

/* 步骤条美化 */
.steps-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
  gap: 0;
  border-radius: 8px;
  padding: 24px 0 16px 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1 1 0;
  min-width: 120px;
  position: relative;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--text-color4);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 6px;
  z-index: 2;
  border: 4px solid var(--white2);
  ;
}

.step-item.active .step-circle {
  background: var(--blue-deep);
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -50%;
  top: 16px;
  width: 100%;
  height: 4px;
  background: var(--text-color4);
  z-index: 0;
}

.step-item.active:nth-child(1):after {
  background-color: var(--blue-deep);
}

.step-item.active p {
  color: var(--blue-deep);
}

.step-item p {
  margin: 0;
  font-size: 15px;
  color: var(--text-color2);
}

.step-item:last-child::after {
  display: none;
}

/* 卡片美化 */
.center-card {
  margin: 0 auto;
  background: var(--white);
  border-radius: 10px;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
  padding: 20px 40px 40px 40px;
}

.layui-card-header {
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid transparent;
}

.tip {
  background: rgba(44, 121, 232, 0.2);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #FBEED5;
}

.layui-card-body {
  color: var(--text-color3);
}

.layui-container {
  width: auto !important;
}

.bug {
  background-color: var(--white2);
}

.layui-icon-ok {
  font-size: 24px;
  color: var(--white);
}

.www {
  text-decoration: underline;
  text-decoration-color: var(--text-color2);
}

.textSelect {
  color: var(--blue-deep) !important;
}

.layui-btn:hover {
  opacity: 1;
}
.hint{
  font-size: 12px;
  margin-left: 140px;
  color: #9C9C9C;
}

.big-title{
  font-size: 16px;
  color: var(--text-color);
  margin: 30px 0 20px;
}

.layui-btn-container .layui-btn {
  width: 100px;
  border-radius: 5px;
}

.layui-form-label{
  width: 106px;
}

.hide{
  opacity: 0;
  margin-left: -120px;
  z-index: -1;
}
.layui-form-item .layui-input-inline:not(.no-input){
 width: 25vw;
}
.btnBox{
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn{
  width: 176px;
  border-radius: 50px;
  margin: 20px 0;
}