@{
  PekHtml.AppendCssFileParts("~/public/css/pageCss/qualified.css");
}
@* 公司资质信息 *@
<div class="main">
  <!-- 面包屑 -->
  <div class="breadBox">
    <a href="/">首页</a>
    <div>></div>
    <a class="textSelect" href="#">
      供应商合作
    </a>
    <div>></div>
    <a class="textSelect" href="#">
      商家入驻申请
    </a>
  </div>
  <div class="layui-container">
    <!-- 步骤条 -->
    <div class="steps-bar">
      <div class="step-item active">
        <div class="step-circle">
          <i class="layui-icon layui-icon-ok"></i>
        </div>
        <p>签订入驻协议</p>
      </div>
      <div class="step-item active">
        <div class="step-circle">
          <i class="layui-icon layui-icon-ok"></i>
        </div>
        <p>公司资质信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">3</div>
        <p>财务资质信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">4</div>
        <p>店铺经营信息</p>
      </div>
      <div class="step-item">
        <div class="step-circle">5</div>
        <p>合同签订</p>
      </div>
      <div class="step-item">
        <div class="step-circle">6</div>
        <p>店铺开通</p>
      </div>
    </div>

    <!-- 协议内容卡片 -->
    <div class="center-card layui-card">

      <div class="layui-card tip">
        <div class="layui-card-header">注意事项:</div>
        <div class="layui-card-body">
          以下所需要上传的电子版资质文件仅支持JPG\GIF\PNG格式图片，大小请控制在1M之内。
        </div>
      </div>


      <form class="layui-form">
        <h3 class="big-title">公司及联系人信息</h3>
        <div class="layui-col-md6 layui-col-xs6">
          <div class="layui-form-item">
            <label class="layui-form-label">公司名称</label>
            <div class="layui-input-inline">
              <input type="text" name="CompanyName" placeholder="请输入公司名称" autocomplete="off" class="layui-input"
                value="1" lay-verify="required">
            </div>
          </div>
        </div>

        <div class="layui-row ">
          <div class="layui-col-md10 layui-col-xs10">
            <div class="layui-form-item">
              <label class="layui-form-label">联动选择框</label>
              <div class="layui-input-inline no-input">
                <select name="quiz1" lay-verify="required">
                  <option value="">请选择省</option>
                  <option value="浙江" selected>浙江省</option>
                  <option value="你的工号">江西省</option>
                  <option value="你最喜欢的老师">福建省</option>
                </select>
              </div>
              <div class="layui-input-inline no-input">
                <select name="quiz2" lay-verify="required">
                  <option value="">请选择市</option>
                  <option value="杭州" selected>杭州</option>
                  <option value="宁波" disabled>宁波</option>
                  <option value="温州">温州</option>
                  <option value="温州">台州</option>
                  <option value="温州">绍兴</option>
                </select>
              </div>
              <div class="layui-input-inline no-input">
                <select name="quiz3" lay-verify="required">
                  <option value="">请选择县/区</option>
                  <option value="西湖区" selected>西湖区</option>
                  <option value="余杭区">余杭区</option>
                  <option value="拱墅区">临安市</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row ">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">公司详细地址</label>
              <div class="layui-input-inline">
                <input type="text" name="address" placeholder="请输入公司详细地址" autocomplete="off" class="layui-input"
                  value="2" lay-verify="required">
              </div>
            </div>
          </div>
        </div>

        @* 地图 *@
        <div class="layui-row">
          <div class="layui-col-md8 layui-col-xs8">
            <div class="layui-form-item">
              <label class="layui-form-label">当前位置</label>
              <div class="layui-input-inline mapBox">
              <div id="allMap" style="width:100%;height:400px;"></div>
              </div>
            </div>
          </div>
        </div>


        <div class="layui-row ">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">注册资金</label>
              <div class="layui-input-inline">
                <input type="number" name="fund" placeholder="请输入注册资金" autocomplete="off" class="layui-input" value="3"
                  lay-verify="required">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">联系人姓名</label>
              <div class="layui-input-inline">
                <input type="text" name="userName" placeholder="请输入姓名" autocomplete="off" class="layui-input" value="4"
                  lay-verify="required">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">联系人电话</label>
              <div class="layui-input-inline">
                <input type="tel" name="Phone" autocomplete="off" lay-affix="clear" placeholder="请输入电话"
                  class="layui-input demo-phone" lay-verify="required|phone" value="13800000000">
              </div>
            </div>
          </div>

        </div>
        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">电子邮箱</label>
              <div class="layui-input-inline">
                <input type="text" name="Mail" placeholder="请输入邮箱" autocomplete="off" class="layui-input"
                  value="<EMAIL>" lay-verify="required|email">
              </div>
            </div>
          </div>

        </div>

        <h3 class="big-title">营业执照信息（副本）</h3>
        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">营业执照号</label>
              <div class="layui-input-inline">
                @* 91110108MAOIEBFH9B *@
                <input type="text" name="BusinessNum" placeholder="请输入营业执照号" autocomplete="off" class="layui-input" 
                  value="91110108MAOIEBFH9B" lay-verify="required|businessnum">
              </div>
            </div>
          </div>
        </div>

        <div class="layui-col-md6 layui-col-xs6">
          <div class="layui-form-item">
            <label class="layui-form-label">@T("营业执照所在地")</label>
            <div class="layui-input-inline">
              <select name="Country" lay-search lay-verify="required">
                <option value="">@T("请选择")</option>
                @foreach (var item in Model.Country)
                {
                  <option value="@item.TwoLetterIsoCode">@item.Name</option>
                }
              </select>
            </div>
          </div>
        </div>


        <div class="layui-row">
          <div class="layui-col-md7 layui-col-xs7">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">营业执照有效期</label>
                <div class="layui-input-inline layui-input-wrap no-input">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-date"></i>
                  </div>
                  <input type="text" name="dateStart" id="date" lay-verify="required|date" placeholder="yyyy-MM-dd"
                    autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label hide">营业执照有效期</label>
                <div class="layui-input-inline layui-input-wrap no-input">
                  <div class="layui-input-prefix">
                    <i class="layui-icon layui-icon-date"></i>
                  </div>
                  <input type="text" name="dateEnd" id="date" placeholder="yyyy-MM-dd" autocomplete="off"
                    class="layui-input">
                </div>
              </div>
              <p class="hint">结束日期不填，表示营业执照为长期</p>
            </div>
          </div>
        </div>


        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item layui-form-text">
              <label class="layui-form-label">法定经营范围</label>
              <div class="layui-input-inline">
                <textarea id="myContent" placeholder="请输入内容" class="layui-textarea"></textarea>
              </div>
            </div>
          </div>
        </div>

        @* 上传文件 *@
        <div class="layui-row">
          <div class="layui-col-md6 layui-col-xs6">
            <div class="layui-form-item">
              <label class="layui-form-label">营业执照电子版</label>
              <div class="layui-btn-container">
                <button type="button" class="layui-btn demo-class-accept layui-btn-primary layui-border"
                  lay-options="{accept: 'file'}">
                  @* <i class="layui-icon layui-icon-upload "></i> *@
                  选择文件
                </button>
                <p class="hint">图片大小请控制在1M之内，请确保图片清晰，文字可辨并有清晰的红色公章。</p>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-row">
          <div class="layui-form-item btnBox">
            <button id="submitBtn" class="layui-btn btn layui-btn-disabled" lay-submit lay-filter="demo1">下一步</button>
          </div>
        </div>
      </form>
    </div>



  </div>
</div>
<div class="bug"></div>
<script type="text/javascript" src="https://api.tianditu.gov.cn/api?v=4.0&tk=4466b7f03b3ccc26a491b9f08d5ee6ef"></script>

<script>
  // 初始化天地图并定位
  function initTianDiTuMap() {
    var map = new T.Map('allMap');
    map.centerAndZoom(new T.LngLat(116.40769, 39.89945), 12); // 默认北京
    // 获取浏览器定位
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(function (position) {
        var lng = position.coords.longitude;
        var lat = position.coords.latitude;
        var point = new T.LngLat(lng, lat);
        map.centerAndZoom(point, 16);
        // 添加标记
        var marker = new T.Marker(point);
        map.addOverLay(marker);
        // 创建信息窗口
        var info = new T.InfoWindow("当前位置");
        // 绑定点击事件显示信息窗口
        marker.addEventListener("click", function () {
          map.openInfoWindow(info, point);
        });
      }, function (err) {
        // 定位失败，保持默认
        console.warn('定位失败', err);
      });
    }
  }
  document.addEventListener('DOMContentLoaded', function () {
    initTianDiTuMap();
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;
      var upload = layui.upload;

      // 自定义营业执照号校验
      form.verify({
        businessnum: function (value, item) {
          // 简单示例：15位或18位数字或字母
          if (!/^([0-9A-Za-z]{15}|[0-9A-Za-z]{18})$/.test(value)) {
            return '请输入正确的营业执照号';
          }
        }
      });

      // 上传文件
      upload.render({
        elem: '.demo-class-accept', // 绑定多个元素
        url: '', // 此处配置你自己的上传接口即可
        size: 1024, // 限制文件大小，单位 KB
        accept: 'images ', // 普通文件
        done: function (res) {
          layer.msg('上传成功');
          console.log(res);
        }
      });


      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值
        console.log("field", field);
        field.Content = $('#myContent').val(); // 获取文本域内容

        // 校验上传文件必填和大小
        var fileInput = $(".demo-class-accept").siblings('input[type="file"]')[0];
        if (!fileInput || fileInput.files.length === 0) {
          layer.msg('请上传营业执照电子版');
          return false;
        }




        return false; // 阻止默认 form 跳转
      });


      // 日期
      laydate.render({
        elem: '#date'
      });

    });

    @* 检查所有必填项填完了解除下一步的layui-btn-disabled*@
      function checkRequiredFields() {
        // 控制按钮
        if (allFilled) {
          $('#submitBtn').removeClass('layui-btn-disabled');
        } else {
          $('#submitBtn').addClass('layui-btn-disabled');
        }
        console.log("allFilled", allFilled);
      }



  });


</script>